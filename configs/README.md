# KULLM-Pro Configuration Files

This directory contains configuration files for training and deployment.

## 📁 Configuration Files

### `train_with_think_tokens.yaml`
Production-ready training configuration for KULLM-Pro v1.1.0 with think token support.

**Key Features:**
- Think token training enabled
- Clean tokenizer with minimal special tokens
- LoRA fine-tuning configuration
- Optimized hyperparameters for reasoning models

**Usage:**
```bash
python src/fine_tune.py --config configs/train_with_think_tokens.yaml
```

## ⚙️ Configuration Structure

### Model Settings
```yaml
model_name: "microsoft/Phi-3.5-mini-instruct"
tokenizer_name: "microsoft/Phi-3.5-mini-instruct"
max_seq_length: 4096
```

### Think Token Settings
```yaml
use_think_tokens: true
think_mode: true
validate_think_tokens: true
```

### Training Hyperparameters
```yaml
learning_rate: 2e-4
batch_size: 4
gradient_accumulation_steps: 4
num_epochs: 3
warmup_ratio: 0.1
```

### LoRA Configuration
```yaml
use_lora: true
lora_r: 16
lora_alpha: 32
lora_dropout: 0.1
target_modules: ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
```

### Special Token Management
```yaml
special_tokens:
  keep_tokens:
    - "<|im_start|>"
    - "<|im_end|>"
    - "<think>"
    - "</think>"
    - "<|endoftext|>"
  remove_tokens:
    - "<tool_call>"
    - "</tool_call>"
    - "<|box_start|>"
    - "<|box_end|>"
    # ... other unnecessary tokens
```

## 🎯 Optimization Notes

### Memory Optimization
- Gradient accumulation reduces memory usage
- LoRA reduces trainable parameters by ~99%
- Mixed precision training (fp16) enabled

### Performance Optimization
- Optimized batch size for RTX 4090
- Cosine learning rate schedule
- Gradient checkpointing for memory efficiency

### Quality Assurance
- Think token validation ensures proper formatting
- Data preprocessing with format checking
- Comprehensive logging and monitoring

## 🔧 Customization

### For Different Hardware
```yaml
# For smaller GPUs (RTX 3080/4080)
batch_size: 2
gradient_accumulation_steps: 8

# For larger GPUs (A100/H100)
batch_size: 8
gradient_accumulation_steps: 2
```

### For Different Models
```yaml
# For larger models (7B+)
lora_r: 32
lora_alpha: 64

# For smaller models (1B-3B)
lora_r: 8
lora_alpha: 16
```

### For Different Datasets
```yaml
# For longer sequences
max_seq_length: 8192

# For shorter sequences
max_seq_length: 2048
```

## 📊 Expected Results

With the default configuration:
- **Training time**: ~2-4 hours (817 samples, RTX 4090)
- **Memory usage**: ~12GB VRAM
- **Think token coverage**: 95%+
- **Model quality**: High reasoning capability with bilingual code-switching

## 🚨 Important Notes

1. **GPU Memory**: Ensure sufficient VRAM for your batch size
2. **Think Tokens**: Validate your data has proper think token formatting
3. **Tokenizer**: Clean tokenizer setup is crucial for optimal performance
4. **Monitoring**: Use Weights & Biases for training monitoring

For more details, see the main [README.md](../README.md) and [training guide](../docs/training.md).

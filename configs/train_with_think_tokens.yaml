# Training configuration for KULLM-Pro with think tokens and cleaned tokenizer
# This config ensures proper think token training and removes unnecessary special tokens

# Model settings
model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  max_length: 8192

# Dataset settings
dataset:
  path: "data/code_switched_GAIR_LIMO_train_817.jsonl"
  train_split: 0.95
  eval_split: 0.05
  think_token_start: "<think>"
  think_token_end: "</think>"

# Think token settings
think_tokens:
  enabled: true
  validate: true
  clean_tokenizer: true
  essential_tokens_only: true

# Training hyperparameters
training:
  learning_rate: 0.0002
  per_device_train_batch_size: 4
  per_device_eval_batch_size: 4
  gradient_accumulation_steps: 4
  num_train_epochs: 3
  warmup_ratio: 0.1
  weight_decay: 0.01
  optimizer: "adamw_torch"
  lr_scheduler_type: "cosine"
  save_strategy: "steps"
  eval_strategy: "steps"
  eval_steps: 100
  logging_steps: 10
  save_total_limit: 2
  save_steps: 500
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  fp16: true
  gradient_checkpointing: true
  dataloader_pin_memory: true
  remove_unused_columns: false
  report_to: ["wandb"]

# LoRA settings
lora:
  enabled: true
  r: 16
  alpha: 32
  dropout: 0.1
  bias: "none"
  target_modules: ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]

# Output settings
output_dir: "outputs/limo-think-tokens"
run_name: "kullm-pro-think-tokens"

# Special token handling
special_tokens:
  keep_tokens:
    - "<|im_start|>"
    - "<|im_end|>"
    - "<think>"
    - "</think>"
    - "<|endoftext|>"
  remove_tokens:
    - "<tool_call>"
    - "</tool_call>"
    - "<|box_start|>"
    - "<|box_end|>"
    - "<|vision_start|>"
    - "<|vision_end|>"
    - "<|vision_pad|>"
    - "<|video_pad|>"
    - "<|image_pad|>"
    - "<|quad_start|>"
    - "<|quad_end|>"
    - "<|object_ref_start|>"
    - "<|object_ref_end|>"
    - "<|fim_prefix|>"
    - "<|fim_middle|>"
    - "<|fim_suffix|>"
    - "<|fim_pad|>"
    - "<|file_sep|>"
    - "<|repo_name|>"

# Data processing
preprocessing:
  format_with_think_tokens: true
  validate_format: true
  max_think_length: 8192
  ensure_think_closure: true  # Ensure every <think> has a </think>

# Weights & Biases settings
wandb:
  enabled: true
  project: "kullm-pro-v1.1"
  entity: null  # Set your wandb entity here
  tags: ["think-tokens", "qwen2.5-7b", "korean-reasoning"]

# Validation
validation:
  check_think_token_balance: true  # Ensure <think> and </think> are balanced
  sample_outputs: true  # Generate sample outputs during training
  think_token_coverage: 0.9  # Require 90% of training examples to have think tokens

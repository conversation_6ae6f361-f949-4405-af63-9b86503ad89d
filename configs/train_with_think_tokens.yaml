# Training configuration for KULLM-Pro with think tokens and cleaned tokenizer
# This config ensures proper think token training and removes unnecessary special tokens

# Model and tokenizer settings
model_name: "Qwen/Qwen2.5-7B-Instruct"
tokenizer_name: "Qwen/Qwen2.5-7B-Instruct"

# Clean tokenizer settings
clean_tokenizer: true  # Remove unnecessary special tokens
essential_tokens_only: true  # Keep only im_start, im_end, think tokens

# Data settings
dataset_path: "data/code_switched_GAIR_LIMO_train_817.jsonl"
max_seq_length: 8192
train_split: 0.95
eval_split: 0.05

# Think token settings
use_think_tokens: true
think_mode: true
validate_think_tokens: true  # Ensure think tokens are properly formatted

# Training hyperparameters
learning_rate: 2e-4
batch_size: 4
gradient_accumulation_steps: 4
num_epochs: 3
warmup_ratio: 0.1
weight_decay: 0.01

# LoRA settings
use_lora: true
lora_r: 16
lora_alpha: 32
lora_dropout: 0.1
target_modules: ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]

# Optimization
optimizer: "adamw_torch"
lr_scheduler_type: "cosine"
save_strategy: "epoch"
evaluation_strategy: "steps"
eval_steps: 100
logging_steps: 10

# Output settings
output_dir: "outputs/limo-think-tokens"
run_name: "kullm-pro-think-tokens"
save_total_limit: 2

# Special token handling
special_tokens:
  keep_tokens:
    - "<|im_start|>"
    - "<|im_end|>"
    - "<think>"
    - "</think>"
    - "<|endoftext|>"
  remove_tokens:
    - "<tool_call>"
    - "</tool_call>"
    - "<|box_start|>"
    - "<|box_end|>"
    - "<|vision_start|>"
    - "<|vision_end|>"
    - "<|vision_pad|>"
    - "<|video_pad|>"
    - "<|image_pad|>"
    - "<|quad_start|>"
    - "<|quad_end|>"
    - "<|object_ref_start|>"
    - "<|object_ref_end|>"
    - "<|fim_prefix|>"
    - "<|fim_middle|>"
    - "<|fim_suffix|>"
    - "<|fim_pad|>"
    - "<|file_sep|>"
    - "<|repo_name|>"

# Data processing
preprocessing:
  format_with_think_tokens: true
  validate_format: true
  max_think_length: 8192
  ensure_think_closure: true  # Ensure every <think> has a </think>

# Logging and monitoring
logging:
  log_level: "info"
  report_to: ["wandb"]
  log_think_token_stats: true  # Log statistics about think token usage

# Weights & Biases settings
wandb:
  project: "kullm-pro-v1.1"
  entity: null  # Set your wandb entity here
  tags: ["think-tokens", "qwen2.5-7b", "korean-reasoning"]
  enabled: true

# Validation
validation:
  check_think_token_balance: true  # Ensure <think> and </think> are balanced
  sample_outputs: true  # Generate sample outputs during training
  think_token_coverage: 0.9  # Require 90% of training examples to have think tokens

#!/usr/bin/env python3
"""
<PERSON><PERSON>t to help fix code-switching behavior and encourage proper think token usage.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chat import Colors

def suggest_prompts_for_think_tokens():
    """Suggest specific prompts that should encourage think token usage."""
    
    print(f"{Colors.CYAN}🎯 How to Get Proper Think Token Responses{Colors.RESET}")
    print("=" * 60)
    
    print(f"{Colors.RED}❌ PROBLEM IDENTIFIED:{Colors.RESET}")
    print("Your model is generating code-switched responses (mixing English and Korean)")
    print("instead of using proper <think> tags for reasoning.")
    print()
    
    print(f"{Colors.GREEN}✅ SOLUTION:{Colors.RESET}")
    print("Use explicit prompts that request think token format:")
    print()
    
    prompts = [
        "Please solve this using <think> tags to show your reasoning: Set A consists of m consecutive integers whose sum is 2m...",
        "Use <think> and </think> tags to show your step-by-step reasoning for this problem: [your math problem]",
        "I want to see your thinking process in <think> tags. Solve: [problem]",
        "Show your work using <think> tags: [problem]",
        "Please use the format <think>reasoning</think> then answer: [problem]"
    ]
    
    print(f"{Colors.YELLOW}🎯 Try these explicit prompts:{Colors.RESET}")
    for i, prompt in enumerate(prompts, 1):
        print(f"{Colors.GREEN}{i}.{Colors.RESET} {prompt}")
    
    print(f"\n{Colors.BLUE}💡 Additional Tips:{Colors.RESET}")
    print("• Start your prompt with 'Please use <think> tags'")
    print("• Be explicit about wanting to see reasoning process")
    print("• If model still code-switches, try: 'Answer in English only using <think> tags'")
    print("• For Korean: '한국어로만 답변하고 <think> 태그를 사용해주세요'")
    
    print(f"\n{Colors.CYAN}🔧 Alternative System Prompt:{Colors.RESET}")
    print("If the default doesn't work, try running with a custom system prompt:")
    print()
    custom_prompt = '''You are a helpful assistant. You must ALWAYS use <think> and </think> tags to show your reasoning process. Do not mix languages. Choose either English or Korean for your entire response.

Format:
<think>
Step 1: [reasoning]
Step 2: [reasoning]
Step 3: [reasoning]
</think>

Final answer: [answer]'''
    
    print(f"{Colors.YELLOW}{custom_prompt}{Colors.RESET}")
    print()
    print(f"Run with: {Colors.GREEN}python chat.py --model_path YOUR_MODEL --sys_prompt \"[paste above prompt]\"{Colors.RESET}")

def explain_code_switching_issue():
    """Explain why the model is code-switching instead of using think tokens."""
    
    print(f"\n{Colors.CYAN}🔍 Why This Is Happening{Colors.RESET}")
    print("=" * 40)
    
    print(f"{Colors.YELLOW}Your model was fine-tuned on code-switched data, so it learned to:{Colors.RESET}")
    print("• Mix English and Korean naturally")
    print("• Use Korean for reasoning ('step by step로 쪼개보자')")
    print("• Switch languages mid-sentence")
    print()
    
    print(f"{Colors.BLUE}This is actually a feature of your model, but for think tokens we want:{Colors.RESET}")
    print("• Clear separation between reasoning and answers")
    print("• Reasoning inside <think> tags")
    print("• Consistent language choice")
    print()
    
    print(f"{Colors.GREEN}The updated system prompt now explicitly discourages code-switching{Colors.RESET}")
    print("and encourages proper think token usage.")

if __name__ == "__main__":
    suggest_prompts_for_think_tokens()
    explain_code_switching_issue()
    
    print(f"\n{Colors.CYAN}🚀 Next Steps:{Colors.RESET}")
    print("1. Try the explicit prompts above")
    print("2. If still code-switching, use the custom system prompt")
    print("3. Be very explicit about wanting <think> tags in your prompts")
    print(f"4. Run: {Colors.GREEN}python chat.py --model_path YOUR_MODEL{Colors.RESET}")

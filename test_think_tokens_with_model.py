#!/usr/bin/env python3
"""
Test script to verify think token functionality with your specific model.
This script will test if the model can generate think tokens properly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chat import Colors, format_think_tokens, load_system_prompt

def test_system_prompt():
    """Test the system prompt loading and content."""
    print(f"{Colors.CYAN}🧪 Testing System Prompt{Colors.RESET}")
    print("=" * 50)
    
    prompt = load_system_prompt()
    print(f"{Colors.GREEN}System Prompt Loaded:{Colors.RESET}")
    print(f"{Colors.YELLOW}{prompt[:200]}...{Colors.RESET}")
    
    # Check if think tokens are mentioned
    if "<think>" in prompt and "</think>" in prompt:
        print(f"{Colors.GREEN}✅ Think tokens are mentioned in system prompt{Colors.RESET}")
    else:
        print(f"{Colors.RED}❌ Think tokens not found in system prompt{Colors.RESET}")
    
    return prompt

def test_think_token_formatting():
    """Test think token color formatting."""
    print(f"\n{Colors.CYAN}🎨 Testing Think Token Formatting{Colors.RESET}")
    print("=" * 50)
    
    test_cases = [
        "Simple answer without think tokens",
        "<think>This is a simple think token</think>The answer is 42.",
        "Before <think>reasoning process here</think> after",
        "<think>Multi-line\nthink token\nwith reasoning</think>Final answer",
        "Multiple <think>first thought</think> and <think>second thought</think> tokens"
    ]
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{Colors.BLUE}Test {i}:{Colors.RESET}")
        print(f"Input: {test}")
        formatted = format_think_tokens(test)
        print(f"Output: {formatted}")
    
    print(f"\n{Colors.GREEN}✅ Think token formatting test complete{Colors.RESET}")

def suggest_test_prompts():
    """Suggest prompts that should trigger think tokens."""
    print(f"\n{Colors.CYAN}💡 Suggested Test Prompts{Colors.RESET}")
    print("=" * 50)
    
    prompts = [
        "Solve this step by step: What is 15% of 240?",
        "Explain how to solve 2x + 5 = 13 showing your work",
        "Debug this Python code: def factorial(n): return n * factorial(n-1)",
        "Compare the pros and cons of Python vs JavaScript",
        "이 수학 문제를 단계별로 풀어주세요: 3x - 7 = 14",
        "What are the steps to make a paper airplane?",
        "How would you approach learning a new programming language?"
    ]
    
    print(f"{Colors.YELLOW}Try these prompts in the chat interface:{Colors.RESET}")
    for i, prompt in enumerate(prompts, 1):
        print(f"{Colors.GREEN}{i}.{Colors.RESET} {prompt}")
    
    print(f"\n{Colors.BLUE}💡 Tips:{Colors.RESET}")
    print(f"• If think tokens don't appear, try adding: 'Please show your reasoning process'")
    print(f"• The model might need explicit instruction to use think tags")
    print(f"• Some models work better with 'step by step' or 'show your work' prompts")

def main():
    """Run all tests."""
    print(f"{Colors.CYAN}🚀 KULLM-Pro Think Token Test Suite{Colors.RESET}")
    print("=" * 60)
    
    # Test system prompt
    test_system_prompt()
    
    # Test formatting
    test_think_token_formatting()
    
    # Suggest test prompts
    suggest_test_prompts()
    
    print(f"\n{Colors.CYAN}🎯 Next Steps:{Colors.RESET}")
    print(f"1. Run the chat interface: {Colors.YELLOW}python chat.py --model_path YOUR_MODEL_PATH{Colors.RESET}")
    print(f"2. Try the suggested prompts above")
    print(f"3. Look for {Colors.THINK_START}<think>{Colors.RESET} and {Colors.THINK_END}</think>{Colors.RESET} tags in responses")
    print(f"4. If no think tokens appear, try more explicit prompting")

if __name__ == "__main__":
    main()

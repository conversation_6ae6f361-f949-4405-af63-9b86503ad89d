#!/usr/bin/env python3
"""
Demo script to showcase think token coloring in KULLM-Pro chat interface.
This script demonstrates how think tokens are displayed with different colors.
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chat import Colors, format_think_tokens


def demo_think_token_coloring():
    """Demonstrate think token coloring functionality."""

    print(f"{Colors.CYAN}🎨 KULLM-Pro Think Token Coloring Demo{Colors.RESET}")
    print("=" * 50)

    # Sample responses with think tokens
    sample_responses = [
        "Let me solve this step by step. <think>First, I need to identify what type of problem this is. It's a quadratic equation: 2x² + 5x - 3 = 0. I can use the quadratic formula: x = (-b ± √(b²-4ac)) / 2a. Here a=2, b=5, c=-3.</think>Using the quadratic formula, x = (-5 ± √(25+24)) / 4 = (-5 ± 7) / 4. So x = 1/2 or x = -3.",
        "안녕하세요! <think>사용자가 한국어로 인사했으니 한국어로 응답하는 것이 좋겠다. 친근하고 도움이 되는 톤으로 답변해야 한다.</think>반갑습니다! 무엇을 도와드릴까요?",
        "I need to analyze this code. <think>Looking at this function, I can see several issues:\n1. The variable 'result' is used before being initialized\n2. The loop condition might cause an infinite loop\n3. There's no error handling for edge cases\n\nI should provide specific suggestions for each issue.</think>Here are the main problems I found in your code and how to fix them...",
        "Let me think about this math problem. <think>This is asking for the derivative of f(x) = x³ + 2x² - 5x + 1.\n\nUsing the power rule:\n- d/dx(x³) = 3x²\n- d/dx(2x²) = 4x  \n- d/dx(-5x) = -5\n- d/dx(1) = 0\n\nSo f'(x) = 3x² + 4x - 5</think>The derivative is f'(x) = 3x² + 4x - 5.",
    ]

    for i, response in enumerate(sample_responses, 1):
        print(f"\n{Colors.BLUE}Example {i}:{Colors.RESET}")
        print(
            f"{Colors.ASSISTANT}🤖 Assistant: {format_think_tokens(response)}{Colors.RESET}"
        )
        print()

    print(f"{Colors.GREEN}✨ Features demonstrated:{Colors.RESET}")
    print(
        f"  • {Colors.THINK_START}<think>{Colors.RESET} and {Colors.THINK_END}</think>{Colors.RESET} tags in orange"
    )
    print(f"  • {Colors.THINK_CONTENT}Think content in gray{Colors.RESET}")
    print(f"  • Support for both English and Korean text")
    print(f"  • Multi-line think sections with proper formatting")
    print(f"  • Mathematical expressions within think tokens")

    print(f"\n{Colors.CYAN}🚀 To use the enhanced chat interface:{Colors.RESET}")
    print(f"  python chat.py --model_path YOUR_MODEL_PATH")
    print(
        f"  python chat.py --model_path YOUR_MODEL_PATH --think_mode False  # Disable think mode"
    )
    print(f"\n{Colors.GREEN}✨ New Features:{Colors.RESET}")
    print(f"  • Real-time streaming generation")
    print(f"  • Think tokens displayed in different colors during streaming")
    print(f"  • Korean language support for user prompts")
    print(f"  • Automatic system prompt loading from code_switch.txt")
    print(f"  • Enhanced error handling and user experience")


if __name__ == "__main__":
    demo_think_token_coloring()

#!/usr/bin/env python3
"""
Test script to verify streaming and think token functionality.
This script simulates the streaming behavior without requiring a full model.
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chat import Colors, format_think_tokens

def simulate_streaming_with_think_tokens():
    """Simulate streaming generation with think tokens."""
    
    print(f"{Colors.CYAN}🧪 Testing Streaming Think Token Display{Colors.RESET}")
    print("=" * 50)
    
    # Simulate a response with think tokens
    response_text = "Let me solve this step by step. <think>I need to break this down: first identify the problem type, then apply the appropriate method. This looks like a quadratic equation.</think>The answer is x = 2."
    
    print(f"{Colors.ASSISTANT}🤖 Assistant: {Colors.RESET}", end="", flush=True)
    
    # Simulate token-by-token streaming
    for i, char in enumerate(response_text):
        print(char, end="", flush=True)
        time.sleep(0.02)  # Simulate streaming delay
    
    print()  # New line
    
    # Now show the formatted version with colors
    print(f"\r{Colors.ASSISTANT}🤖 Assistant: {format_think_tokens(response_text)}{Colors.RESET}")
    
    print(f"\n{Colors.GREEN}✅ Streaming simulation complete!{Colors.RESET}")
    print(f"{Colors.YELLOW}In the actual chat interface:{Colors.RESET}")
    print(f"  • Text streams in real-time as the model generates")
    print(f"  • Think tokens are highlighted with colors after generation")
    print(f"  • Korean input is fully supported")
    print(f"  • System prompts are auto-loaded from code_switch.txt")

def test_korean_support():
    """Test Korean language support."""
    print(f"\n{Colors.CYAN}🇰🇷 Testing Korean Language Support{Colors.RESET}")
    print("=" * 50)
    
    korean_examples = [
        "안녕하세요! 무엇을 도와드릴까요?",
        "수학 문제를 풀어보겠습니다. <think>이 문제는 이차방정식이네요. 근의 공식을 사용해야겠습니다.</think>답은 x = 3입니다.",
        "코드를 분석해보겠습니다. <think>이 함수에서 몇 가지 문제점을 발견했습니다.</think>다음과 같이 수정하시면 됩니다."
    ]
    
    for i, example in enumerate(korean_examples, 1):
        print(f"\n{Colors.BLUE}Example {i}:{Colors.RESET}")
        formatted = format_think_tokens(example)
        print(f"{Colors.ASSISTANT}🤖 Assistant: {formatted}{Colors.RESET}")
    
    print(f"\n{Colors.GREEN}✅ Korean language support verified!{Colors.RESET}")

if __name__ == "__main__":
    simulate_streaming_with_think_tokens()
    test_korean_support()
    
    print(f"\n{Colors.CYAN}🚀 Ready to use enhanced chat interface!{Colors.RESET}")
    print(f"Run: {Colors.YELLOW}python chat.py --model_path YOUR_MODEL_PATH{Colors.RESET}")

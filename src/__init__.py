"""
KULLM Pro: Korean Reasoning Language Model with Think Tokens

A production-ready framework for fine-tuning reasoning models using
structured thinking tokens. KULLM Pro learns to reason step-by-step using `<think>`
and `</think>` tokens, enabling transparent and improved mathematical problem-solving
capabilities.

This package provides two main components:

1. **Code Switching Module** (`code_switch.py`):
   - Process any Hugging Face dataset with flexible parameters
   - Generate Korean translations using OpenAI Batch API
   - Output original and code-switched JSONL files
   - Support for dataset filtering and sample selection

2. **Fine-tuning Module** (`fine_tune.py`):
   - LoRA (Low-Rank Adaptation) fine-tuning for efficient training
   - Advanced training features with Accelerate support
   - Weights & Biases integration for experiment tracking
   - Checkpoint management and resumable training
   - Think token integration for reasoning models

3. **Utilities Module** (`utils`):
   - Data processing utilities
   - Model utilities and helpers
   - Configuration management
   - Logging setup

Example Usage:
    ```python
    # Code switching
    python src/code_switch.py --dataset_name="GAIR/LIMO" --split="train" --n_samples=300

    # Fine-tuning
    python src/fine_tune.py --data_file="path/to/training_data.jsonl" --model_name="Qwen/Qwen2.5-7B-Instruct"
    ```

Requirements:
    - Python 3.8+
    - PyTorch 2.0+
    - Transformers 4.44+
    - CUDA-compatible GPU (recommended for fine-tuning)

License:
    Apache 2.0 License - see LICENSE file for details

Authors: <AUTHORS>

Version:
    2.0.0 - Reorganized production-ready structure with code switching and fine-tuning
"""

__version__ = "2.0.0"
__author__ = "KULLM Pro Development Team"
__email__ = "<EMAIL>"
__license__ = "Apache-2.0"
__description__ = "Korean Reasoning Language Model with Think Tokens"
__url__ = "https://github.com/junkim100/KULLM-Pro"

# Define public API
__all__ = [
    # Package metadata
    "__version__",
    "__author__",
    "__license__",
    "__description__",
]

# KULLM-Pro Enhanced Chat Interface Usage Guide

## 🚀 Quick Start

The enhanced chat interface is located in the **root directory** as `chat.py`, not in `utils/`.

### Correct Usage:

```bash
# Run from the root directory of KULLM-Pro
python chat.py --model_path /data_x/junkim100/projects/KULLM/KULLM-Pro/outputs/limo/code_switched-817-lora-merged --max_new_tokens 8192
```

### ❌ Incorrect (what you tried):
```bash
python utils/chat.py --model_path ...  # This file doesn't exist!
```

## 🎯 Think Token Features

The enhanced chat interface includes:

1. **Automatic Think Token Prompting**: The system prompt encourages the model to use `<think>` tags
2. **Color-coded Display**: Think tokens appear in different colors
3. **Streaming Support**: Real-time text generation
4. **Korean Language Support**: Full UTF-8 support

## 🔧 Command Line Options

```bash
python chat.py MODEL_PATH [OPTIONS]

Options:
  --sys_prompt TEXT         Custom system prompt (optional)
  --max_new_tokens INT     Maximum tokens to generate (default: 512)
  --think_mode BOOL        Enable/disable think mode (default: True)
  --temperature FLOAT      Sampling temperature (default: 1.0)
  --do_sample BOOL         Enable sampling (default: False)
```

## 📝 Example Commands

```bash
# Basic usage with your model
python chat.py /data_x/junkim100/projects/KULLM/KULLM-Pro/outputs/limo/code_switched-817-lora-merged

# With more tokens and sampling
python chat.py /data_x/junkim100/projects/KULLM/KULLM-Pro/outputs/limo/code_switched-817-lora-merged \
  --max_new_tokens 8192 \
  --temperature 0.7 \
  --do_sample True

# With custom system prompt
python chat.py /data_x/junkim100/projects/KULLM/KULLM-Pro/outputs/limo/code_switched-817-lora-merged \
  --sys_prompt "You are a helpful math tutor. Always show your work using <think> tags."
```

## 🎨 Think Token Display

When the model generates responses with think tokens, you'll see:

- **Orange tags**: `<think>` and `</think>` 
- **Gray content**: The reasoning process inside think tokens
- **Normal text**: The final answer in regular assistant color

## 🐛 Troubleshooting

### Think tokens not appearing?

1. **Check the system prompt**: The default prompt now encourages think token usage
2. **Try explicit prompting**: Ask "Please show your reasoning using think tags"
3. **Model capability**: Ensure your model was trained with think tokens

### Example prompts that encourage think tokens:

```
"Solve this math problem step by step: 2x + 5 = 13"
"Explain how photosynthesis works, showing your reasoning process"
"Debug this Python code and show your analysis"
```

## 🌏 Korean Support

The interface fully supports Korean input:

```
"이 수학 문제를 단계별로 풀어주세요: 2x + 5 = 13"
"광합성이 어떻게 작동하는지 설명해주세요"
```

## 💡 Tips

1. **Use descriptive prompts**: Ask for step-by-step reasoning
2. **Be patient**: Streaming shows text as it generates
3. **Use 'clear'**: Type 'clear' to reset conversation
4. **Use 'exit'**: Type 'exit' to quit the interface

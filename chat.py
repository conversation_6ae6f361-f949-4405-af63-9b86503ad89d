import os
import time
import torch
import re
import warnings
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
from pathlib import Path

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")


# ANSI color codes for terminal output
class Colors:
    RESET = "\033[0m"
    BOLD = "\033[1m"

    # Standard colors
    RED = "\033[91m"
    GREEN = "\033[92m"
    YELLOW = "\033[93m"
    BLUE = "\033[94m"
    MAGENTA = "\033[95m"
    CYAN = "\033[96m"
    WHITE = "\033[97m"

    # Think token colors (distinctive)
    THINK_START = "\033[38;5;208m"  # Orange
    THINK_CONTENT = "\033[38;5;245m"  # Gray
    THINK_END = "\033[38;5;208m"  # Orange

    # Role colors
    USER = "\033[93m"  # Yellow
    ASSISTANT = "\033[95m"  # Magenta
    SYSTEM = "\033[92m"  # Green


def format_think_tokens(text):
    """Format text with colored think tokens."""
    if not text:
        return text

    # Pattern to match think tokens and their content
    think_pattern = r"(<think>)(.*?)(</think>)"

    def replace_think(match):
        start_tag = match.group(1)
        content = match.group(2)
        end_tag = match.group(3)

        return (
            f"{Colors.THINK_START}{start_tag}{Colors.RESET}"
            f"{Colors.THINK_CONTENT}{content}{Colors.RESET}"
            f"{Colors.THINK_END}{end_tag}{Colors.RESET}"
        )

    # Replace think tokens with colored versions
    formatted_text = re.sub(think_pattern, replace_think, text, flags=re.DOTALL)

    return formatted_text


def get_pipeline(path, tokenizer):
    model = AutoModelForCausalLM.from_pretrained(
        path, torch_dtype=torch.float16, device_map="auto", trust_remote_code=True
    )
    print("Model loaded")
    generator = pipeline(
        "text-generation",
        model=model,
        tokenizer=tokenizer,
        pad_token_id=tokenizer.pad_token_id,
        eos_token_id=[tokenizer.eos_token_id, 128001],
    )
    return generator


def main(
    model_path: str,
    sys_prompt: str = None,
    max_new_tokens: int = 512,
    think_mode: bool = True,
    **kwargs,
):
    print(f"{Colors.CYAN}🚀 Loading KULLM-Pro Chat Interface...{Colors.RESET}")
    print(f"{Colors.BLUE}Model path: {model_path}{Colors.RESET}")

    # Load tokenizer with UTF-8 support for Korean
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    tokenizer.pad_token = tokenizer.eos_token
    tokenizer.pad_token_id = tokenizer.eos_token_id

    # Load system prompt from file if not provided
    if sys_prompt is None:


    print(
        f"{Colors.BLUE}BOS token: {tokenizer.bos_token} (ID: {tokenizer.bos_token_id}){Colors.RESET}"
    )
    print(
        f"{Colors.BLUE}EOS token: {tokenizer.eos_token} (ID: {tokenizer.eos_token_id}){Colors.RESET}"
    )
    print(
        f"{Colors.BLUE}Think mode: {'🧠 Enabled' if think_mode else '❌ Disabled'}{Colors.RESET}"
    )

    pipe = get_pipeline(model_path, tokenizer)
    messages = []
    messages.append({"role": "system", "content": sys_prompt})

    print(f"\n{Colors.CYAN}💬 KULLM-Pro Chat Interface Ready!{Colors.RESET}")
    print(
        f"{Colors.YELLOW}Commands: 'clear' to reset conversation, 'exit' to quit{Colors.RESET}"
    )
    print(
        f"{Colors.YELLOW}Korean input is fully supported! 한국어 입력도 지원합니다!{Colors.RESET}\n"
    )
    while 1:
        try:
            # Use colored prompt for input
            input_ = input(f"{Colors.BLUE}👤 Enter instruction: {Colors.RESET}")

            if input_ == "clear":
                messages = []
                if sys_prompt:
                    messages.append({"role": "system", "content": sys_prompt})
                os.system("clear")
                print(f"{Colors.GREEN}🧹 Conversation cleared!{Colors.RESET}\n")
                continue
            elif input_ == "exit":
                print(f"{Colors.CYAN}👋 Goodbye!{Colors.RESET}")
                break
            elif input_.strip() == "":
                continue

            messages.append({"role": "user", "content": input_})
            os.system("clear")

            # Display conversation history with colors
            for m in messages[:-1]:
                role = m["role"]
                content = m["content"]

                if role == "system":
                    print(
                        f"{Colors.SYSTEM}🔧 System: {content[:100]}{'...' if len(content) > 100 else ''}{Colors.RESET}"
                    )
                elif role == "user":
                    print(f"{Colors.USER}👤 User: {content}{Colors.RESET}")
                elif role == "assistant":
                    formatted_content = format_think_tokens(content)
                    print(
                        f"{Colors.ASSISTANT}🤖 Assistant: {formatted_content}{Colors.RESET}"
                    )

            # Show current user input
            print(f"{Colors.USER}👤 User: {input_}{Colors.RESET}")

            start = time.time()

            # Apply chat template with think mode support
            template_kwargs = {"add_generation_prompt": True}
            if think_mode is not None:
                template_kwargs["think_mode"] = think_mode

            text = tokenizer.apply_chat_template(
                messages, tokenize=False, **template_kwargs
            )

            # Generate response
            result = pipe(
                text,
                return_full_text=False,
                clean_up_tokenization_spaces=True,
                max_new_tokens=max_new_tokens,
                do_sample=kwargs.get("do_sample", False),
                **kwargs,
            )[0]["generated_text"]

            messages.append({"role": "assistant", "content": result})

            # Display assistant response with think token formatting
            formatted_result = format_think_tokens(result)
            print(f"{Colors.ASSISTANT}🤖 Assistant: {formatted_result}{Colors.RESET}")

            # Show timing info
            elapsed = time.time() - start
            print(f"{Colors.CYAN}⏱️  Response time: {elapsed:.2f}s{Colors.RESET}\n")

        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}⚠️  Interrupted by user{Colors.RESET}")
            break
        except Exception as e:
            print(f"{Colors.RED}❌ Error: {str(e)}{Colors.RESET}")
            continue


if __name__ == "__main__":
    import fire

    fire.Fire(main)

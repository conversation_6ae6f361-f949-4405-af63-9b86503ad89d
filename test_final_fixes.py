#!/usr/bin/env python3
"""
Test script to verify the final fixes for think tokens, duplicate output, and tool_call issues.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chat import Colors, format_think_tokens, load_system_prompt

def test_system_prompt():
    """Test the updated system prompt that preserves code-switching."""
    print(f"{Colors.CYAN}🧪 Testing Updated System Prompt{Colors.RESET}")
    print("=" * 50)
    
    prompt = load_system_prompt()
    print(f"{Colors.GREEN}System Prompt:{Colors.RESET}")
    print(f"{Colors.YELLOW}{prompt}{Colors.RESET}")
    
    # Check for key features
    features = [
        ("Think tag requirement", "<think>" in prompt and "</think>" in prompt),
        ("Code-switching encouraged", "code switch" in prompt.lower() or "english and korean" in prompt.lower()),
        ("Clear example provided", "EXAMPLE:" in prompt),
        ("Mandatory format", "MUST" in prompt),
        ("Anti-repetition", "NOT repeat" in prompt),
    ]
    
    print(f"\n{Colors.BLUE}✅ Features Verified:{Colors.RESET}")
    for feature, present in features:
        status = "✅" if present else "❌"
        print(f"  {status} {feature}")

def test_think_token_examples():
    """Test think token formatting with code-switched examples."""
    print(f"\n{Colors.CYAN}🎨 Testing Think Token Formatting{Colors.RESET}")
    print("=" * 50)
    
    test_cases = [
        # Case 1: Code-switched think token (like your model generates)
        "<think>Okay, so 이 문제는 math problem이야. Let me solve step by step...</think>The answer is 42.",
        
        # Case 2: Korean-heavy think token
        "<think>음, step by step으로 쪼개보자. 먼저 variables를 identify해야 해.</think>답은 33입니다.",
        
        # Case 3: English-heavy think token
        "<think>Let me analyze this problem. 이건 consecutive integers에 관한 문제네.</think>The answer is m = 201.",
        
        # Case 4: Multi-line code-switched
        "<think>\nOkay, so 이 문제는 두 집합이 있어.\n- Set A는 m개의 consecutive integers\n- Set B는 2m개의 consecutive integers\nLet me solve systematically...\n</think>\n\nFinal answer: m = 201",
    ]
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{Colors.BLUE}Test Case {i}:{Colors.RESET}")
        print(f"Input: {test[:60]}...")
        formatted = format_think_tokens(test)
        print(f"Output: {formatted}")

def explain_fixes():
    """Explain what was fixed."""
    print(f"\n{Colors.CYAN}🔧 Final Fixes Applied{Colors.RESET}")
    print("=" * 40)
    
    fixes = [
        ("🎯 System Prompt", "Preserved code-switching, stronger think tag requirement"),
        ("🔄 Duplicate Output", "Fixed with proper line clearing (\\033[A\\033[K)"),
        ("🚫 Tool Call Cleanup", "Remove <tool_call> and <|im_end|> tokens from output"),
        ("⚙️ EOS Handling", "Better EOS token handling with multiple IDs"),
        ("🎨 Display Logic", "Stream first, then replace with colored version once"),
    ]
    
    for fix_type, description in fixes:
        print(f"{fix_type}: {description}")

def suggest_test_commands():
    """Suggest test commands."""
    print(f"\n{Colors.CYAN}🚀 Test Commands{Colors.RESET}")
    print("=" * 30)
    
    print(f"{Colors.YELLOW}Run the fixed chat interface:{Colors.RESET}")
    print("python chat.py --model_path /data_x/junkim100/projects/KULLM/KULLM-Pro/outputs/limo/code_switched-817-lora-merged --max_new_tokens 8192")
    
    print(f"\n{Colors.YELLOW}Test prompts that should work:{Colors.RESET}")
    prompts = [
        "Set A consists of m consecutive integers whose sum is 2m...",
        "Solve this step by step: What is 15% of 240?",
        "이 수학 문제를 풀어주세요: 2x + 5 = 13",
        "Explain how photosynthesis works",
    ]
    
    for i, prompt in enumerate(prompts, 1):
        print(f"{Colors.GREEN}{i}.{Colors.RESET} {prompt}")
    
    print(f"\n{Colors.BLUE}Expected Results:{Colors.RESET}")
    print("• ✅ Response starts with <think> tags")
    print("• ✅ Think tokens appear in orange/gray colors")
    print("• ✅ Code-switching within think tags")
    print("• ✅ Response appears only once (no duplicates)")
    print("• ✅ No <tool_call> or <|im_end|> tokens visible")
    print("• ✅ Clean, properly formatted output")

if __name__ == "__main__":
    test_system_prompt()
    test_think_token_examples()
    explain_fixes()
    suggest_test_commands()
    
    print(f"\n{Colors.CYAN}🎉 All fixes applied successfully!{Colors.RESET}")
    print("The chat interface should now work correctly with:")
    print("1. Think tokens with code-switching")
    print("2. No duplicate output")
    print("3. No unwanted tool_call tokens")
    print("4. Proper color formatting")
